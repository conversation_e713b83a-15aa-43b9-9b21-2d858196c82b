# API Configuration
VITE_API_BASE_URL=http://localhost:7167
VITE_CUSTOMER_API_URL=http://test.localhost:7167
VITE_SUBSCRIPTION_API_URL=http://eg.localhost:7167

# Environment
VITE_NODE_ENV=development

# Application Settings
VITE_APP_NAME=LowCalories Dashboard
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_DEBUG=true
VITE_ENABLE_MOCK_DATA=false

# API Endpoints
VITE_CUSTOMERS_ENDPOINT=/swagger/index.html
VITE_SUBSCRIPTIONS_ENDPOINT=/api/v1/Subscriptions/GetAllSubscriptions
VITE_CREATE_SUBSCRIPTIONS_ENDPOINT=/api/v1/CreateSubscriptions/CreateSubscriptions

# Upload Configuration
VITE_MAX_FILE_SIZE=5242880
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true
VITE_DEFAULT_LANGUAGE=en

# Development
VITE_SHOW_DEV_TOOLS=true
VITE_LOG_LEVEL=debug
