{"name": "lowcalories-dashboard", "private": true, "version": "1.2.0", "type": "module", "description": "A modern meal subscription management dashboard built with React and Tailwind CSS", "keywords": ["react", "dashboard", "meal-subscription", "tailwindcss", "vite", "subscription-management"], "author": "LowCalories Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/lowcalories-dashboard.git"}, "bugs": {"url": "https://github.com/yourusername/lowcalories-dashboard/issues"}, "homepage": "https://github.com/yourusername/lowcalories-dashboard#readme", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "preview": "vite preview", "clean": "rm -rf dist node_modules/.vite", "type-check": "tsc --noEmit"}, "dependencies": {"lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "zustand": "^4.5.7"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^4.5.14"}}