import React from 'react'
import { createPortal } from 'react-dom'
import { useToast } from '../../contexts/ToastContext'
import Toast from './Toast'

const ToastContainer = () => {
  const { toasts } = useToast()

  if (toasts.length === 0) return null

  return createPortal(
    <div className="fixed top-4 right-4 z-50 max-w-sm w-full space-y-2">
      {toasts.map(toast => (
        <Toast key={toast.id} toast={toast} />
      ))}
    </div>,
    document.body
  )
}

export default ToastContainer
